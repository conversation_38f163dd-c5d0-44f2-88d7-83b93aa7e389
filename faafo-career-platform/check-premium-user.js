const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const prisma = new PrismaClient();

async function checkPremiumUser() {
  try {
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      select: {
        id: true,
        email: true,
        password: true,
        emailVerified: true,
        failedLoginAttempts: true,
        lockedUntil: true,
        subscriptionStatus: true,
        subscriptionTier: true
      }
    });
    
    if (!user) {
      console.log('❌ Premium user not found');
      return;
    }
    
    console.log('👤 Premium User Details:');
    console.log('Email:', user.email);
    console.log('Email Verified:', user.emailVerified ? '✅' : '❌');
    console.log('Subscription Status:', user.subscriptionStatus);
    console.log('Subscription Tier:', user.subscriptionTier);
    console.log('Failed Login Attempts:', user.failedLoginAttempts);
    console.log('Locked Until:', user.lockedUntil || 'Not locked');
    
    // Test common passwords
    const testPasswords = ['premium', 'testpassword', 'password123', 'password', 'Premium123!', 'test123', 'Password123!'];
    
    console.log('\n🔍 Testing passwords:');
    for (const pwd of testPasswords) {
      const isValid = await bcrypt.compare(pwd, user.password);
      console.log(`   ${pwd}: ${isValid ? '✅ MATCH' : '❌ No match'}`);
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkPremiumUser();
