const path = require('path')

module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  rootDir: path.resolve(__dirname, '../../../..'),
  setupFilesAfterEnv: ['<rootDir>/jest.setup.minimal.ts'],
  setupFiles: ['<rootDir>/jest.polyfills.js'],

  displayName: 'Unit Tests',
  testMatch: [
    '**/__tests__/**/*.test.(ts|tsx|js|jsx)',
    '**/*.test.(ts|tsx|js|jsx)'
  ],
  testPathIgnorePatterns: [
    '/node_modules/',
    '/integration/',
    '/e2e/',
    '/performance/'
  ],

  // Transform configuration to handle JSX/TSX properly
  transform: {
    '^.+\\.(ts|tsx)$': ['ts-jest', {
      tsconfig: {
        jsx: 'react-jsx',
        esModuleInterop: true,
        allowSyntheticDefaultImports: true,
        target: 'ES2020',
        module: 'ESNext',
        moduleResolution: 'node',
      },
      isolatedModules: true,
    }],
    '^.+\\.(js|jsx)$': ['babel-jest', {
      presets: [
        ['@babel/preset-env', { targets: { node: 'current' } }],
        ['@babel/preset-react', { runtime: 'automatic' }]
      ]
    }]
  },

  moduleNameMapper: {
    '^@/components/(.*)$': '<rootDir>/src/components/$1',
    '^@/lib/(.*)$': '<rootDir>/src/lib/$1',
    '^@/app/(.*)$': '<rootDir>/src/app/$1',
    '^@/types/(.*)$': '<rootDir>/src/types/$1',
    '^@/utils/(.*)$': '<rootDir>/src/lib/utils/$1',
    '^@/hooks/(.*)$': '<rootDir>/src/hooks/$1',
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    // Mock Node.js specific modules that don't work in Jest/jsdom
    '^openid-client$': '<rootDir>/testing/mocks/openid-client.js',
    '^jose$': '<rootDir>/testing/mocks/jose.js',
    '^oauth4webapi$': '<rootDir>/testing/mocks/oauth4webapi.js',
    // Mock API routes that have complex Next.js syntax
    '^@/app/api/(.*)$': '<rootDir>/testing/mocks/api-routes.js',
    // Mock next-auth
    '^next-auth/react$': '<rootDir>/testing/mocks/next-auth.js',
    '^next-auth/next$': '<rootDir>/testing/mocks/next-auth-server.js',
    // Mock Prisma
    '^@/lib/prisma$': '<rootDir>/__mocks__/lib/prisma.js',

  },

  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.*',
    '!src/**/*.test.*',
    '!src/**/*.spec.*'
  ],
  coverageThreshold: {
    global: {
      branches: 50,
      functions: 50,
      lines: 50,
      statements: 50
    }
  },

  testTimeout: 30000,
  maxWorkers: '50%'
}
