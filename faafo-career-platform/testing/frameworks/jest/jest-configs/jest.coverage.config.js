module.exports = {
  getCollectCoverageFrom: () => [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.*',
    '!src/**/*.test.*',
    '!src/**/*.spec.*',
    '!src/types/**/*'
  ],
  
  getCoverageReporters: () => [
    'text-summary',
    'lcov',
    'html'
  ],
  
  getCoverageThreshold: () => ({
    global: {
      branches: 50,
      functions: 50,
      lines: 50,
      statements: 50
    }
  })
}
