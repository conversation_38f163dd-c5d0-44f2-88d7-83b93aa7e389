// Mock for next-auth/react
const mockSession = {
  user: {
    id: 'test-user-id',
    email: '<EMAIL>',
    name: 'Test User'
  },
  expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
};

const mockUseSession = jest.fn(() => ({
  data: mockSession,
  status: 'authenticated',
  update: jest.fn()
}));

const mockSignIn = jest.fn().mockResolvedValue({ ok: true });
const mockSignOut = jest.fn().mockResolvedValue({ ok: true });
const mockGetSession = jest.fn().mockResolvedValue(mockSession);

module.exports = {
  useSession: mockUseSession,
  signIn: mockSignIn,
  signOut: mockSignOut,
  getSession: mockGetSession,
  SessionProvider: ({ children }) => children,
  
  // Reset all mocks
  resetMocks: () => {
    mockUseSession.mockClear();
    mockSignIn.mockClear();
    mockSignOut.mockClear();
    mockGetSession.mockClear();
  },
  
  // Helper to set session state
  setMockSession: (session) => {
    mockUseSession.mockReturnValue({
      data: session,
      status: session ? 'authenticated' : 'unauthenticated',
      update: jest.fn()
    });
  },
  
  // Helper to set loading state
  setMockLoading: () => {
    mockUseSession.mockReturnValue({
      data: null,
      status: 'loading',
      update: jest.fn()
    });
  },
  
  // Helper to set unauthenticated state
  setMockUnauthenticated: () => {
    mockUseSession.mockReturnValue({
      data: null,
      status: 'unauthenticated',
      update: jest.fn()
    });
  }
};
