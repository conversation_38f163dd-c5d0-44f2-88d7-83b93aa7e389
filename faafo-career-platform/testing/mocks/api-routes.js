// Mock for API routes
const { NextResponse } = require('next/server');

// Mock API route handlers
const mockApiHandler = jest.fn().mockResolvedValue(
  NextResponse.json({ success: true, data: {} })
);

const mockErrorHandler = jest.fn().mockRejectedValue(
  new Error('Mock API error')
);

const mockAuthenticatedHandler = jest.fn().mockImplementation(async (request) => {
  // Mock authentication check
  const authHeader = request.headers.get('authorization');
  if (!authHeader) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  return NextResponse.json({ success: true, data: { userId: 'mock-user-id' } });
});

// Export common API route handlers
module.exports = {
  // HTTP Methods
  GET: mockApiHandler,
  POST: mockApiHandler,
  PUT: mockApiHandler,
  PATCH: mockApiHandler,
  DELETE: mockApiHandler,
  
  // Specialized handlers
  authenticatedGET: mockAuthenticatedHandler,
  authenticatedPOST: mockAuthenticatedHandler,
  authenticatedPATCH: mockAuthenticatedHandler,
  authenticatedDELETE: mockAuthenticatedHandler,
  
  // Error handlers
  errorGET: mockErrorHandler,
  errorPOST: mockErrorHandler,
  
  // Mock utilities
  mockSuccess: (data = {}) => NextResponse.json({ success: true, data }),
  mockError: (message = 'Mock error', status = 500) => 
    NextResponse.json({ error: message }, { status }),
  mockUnauthorized: () => 
    NextResponse.json({ error: 'Unauthorized' }, { status: 401 }),
  mockNotFound: () => 
    NextResponse.json({ error: 'Not found' }, { status: 404 }),
  mockValidationError: (errors = []) => 
    NextResponse.json({ error: 'Validation failed', errors }, { status: 400 }),
    
  // Reset all mocks
  resetMocks: () => {
    mockApiHandler.mockClear();
    mockErrorHandler.mockClear();
    mockAuthenticatedHandler.mockClear();
  }
};
