// Mock for UnifiedAuthenticationService
const mockValidationResult = {
  isValid: true,
  userId: 'test-user-id',
  user: {
    id: 'test-user-id',
    email: '<EMAIL>',
    name: 'Test User',
    emailVerified: null
  }
};

const mockSessionAccessResult = {
  isValid: true,
  userId: 'test-user-id',
  sessionId: 'test-session-id',
  hasAccess: true
};

const mockUnifiedAuthenticationService = {
  validateSession: jest.fn().mockResolvedValue(mockValidationResult),
  validateSessionAccess: jest.fn().mockResolvedValue(mockSessionAccessResult),
  validateSessionStateTransition: jest.fn().mockReturnValue({
    isValid: true,
    canTransition: true
  }),
  
  // Reset all mocks
  resetMocks: () => {
    mockUnifiedAuthenticationService.validateSession.mockClear();
    mockUnifiedAuthenticationService.validateSessionAccess.mockClear();
    mockUnifiedAuthenticationService.validateSessionStateTransition.mockClear();
  },
  
  // Helper to set validation failure
  setValidationFailure: (error = 'Authentication failed', statusCode = 401) => {
    mockUnifiedAuthenticationService.validateSession.mockResolvedValue({
      isValid: false,
      error: error,
      statusCode: statusCode
    });
  },
  
  // Helper to set access denied
  setAccessDenied: () => {
    mockUnifiedAuthenticationService.validateSessionAccess.mockResolvedValue({
      isValid: false,
      hasAccess: false,
      error: 'Access denied'
    });
  }
};

module.exports = {
  UnifiedAuthenticationService: mockUnifiedAuthenticationService
};
