// Mock for jose module
module.exports = {
  jwtVerify: jest.fn().mockResolvedValue({
    payload: {
      sub: 'mock-user-id',
      email: '<EMAIL>',
      name: 'Mock User',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600
    },
    protectedHeader: {
      alg: 'RS256',
      typ: 'JWT'
    }
  }),
  
  SignJWT: class MockSignJWT {
    constructor(payload) {
      this.payload = payload;
      return this;
    }
    
    setProtectedHeader() {
      return this;
    }
    
    setIssuedAt() {
      return this;
    }
    
    setExpirationTime() {
      return this;
    }
    
    setIssuer() {
      return this;
    }
    
    setAudience() {
      return this;
    }
    
    async sign() {
      return 'mock-jwt-token';
    }
  },
  
  importJWK: jest.fn().mockResolvedValue({}),
  importSPKI: jest.fn().mockResolvedValue({}),
  importPKCS8: jest.fn().mockResolvedValue({}),
  
  generateKeyPair: jest.fn().mockResolvedValue({
    publicKey: {},
    privateKey: {}
  }),
  
  errors: {
    JWTExpired: class MockJWTExpired extends Error {
      constructor(message) {
        super(message);
        this.name = 'JWTExpired';
      }
    },
    JWTInvalid: class MockJWTInvalid extends Error {
      constructor(message) {
        super(message);
        this.name = 'JWTInvalid';
      }
    }
  }
};
