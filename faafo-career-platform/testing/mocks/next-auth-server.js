// Mock for next-auth/next (server-side)
const mockSession = {
  user: {
    id: 'test-user-id',
    email: '<EMAIL>',
    name: 'Test User',
    role: 'user'
  },
  expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
};

const mockAdminSession = {
  user: {
    id: 'admin-user-id',
    email: '<EMAIL>',
    name: 'Admin User',
    role: 'admin'
  },
  expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
};

const mockGetServerSession = jest.fn(() => Promise.resolve(mockSession));

module.exports = {
  getServerSession: mockGetServerSession,
  
  // Helper functions for tests
  setMockSession: (session) => {
    mockGetServerSession.mockResolvedValue(session);
  },
  
  setMockAdminSession: () => {
    mockGetServerSession.mockResolvedValue(mockAdminSession);
  },
  
  setMockUnauthenticated: () => {
    mockGetServerSession.mockResolvedValue(null);
  },
  
  resetMocks: () => {
    mockGetServerSession.mockClear();
    mockGetServerSession.mockResolvedValue(mockSession);
  }
};
