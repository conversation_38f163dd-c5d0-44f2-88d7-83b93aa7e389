// Mock for oauth4webapi module
module.exports = {
  discoveryRequest: jest.fn().mockResolvedValue(new Response(JSON.stringify({
    authorization_endpoint: 'https://mock-auth.com/auth',
    token_endpoint: 'https://mock-auth.com/token',
    userinfo_endpoint: 'https://mock-auth.com/userinfo',
    jwks_uri: 'https://mock-auth.com/jwks'
  }))),
  
  processDiscoveryResponse: jest.fn().mockResolvedValue({
    authorization_endpoint: 'https://mock-auth.com/auth',
    token_endpoint: 'https://mock-auth.com/token',
    userinfo_endpoint: 'https://mock-auth.com/userinfo',
    jwks_uri: 'https://mock-auth.com/jwks'
  }),
  
  authorizationCodeGrantRequest: jest.fn().mockResolvedValue(new Response(JSON.stringify({
    access_token: 'mock-access-token',
    token_type: 'Bearer',
    expires_in: 3600,
    refresh_token: 'mock-refresh-token'
  }))),
  
  processAuthorizationCodeOpenIDResponse: jest.fn().mockResolvedValue({
    access_token: 'mock-access-token',
    token_type: 'Bearer',
    expires_in: 3600,
    id_token: 'mock-id-token'
  }),
  
  userInfoRequest: jest.fn().mockResolvedValue(new Response(JSON.stringify({
    sub: 'mock-user-id',
    email: '<EMAIL>',
    name: 'Mock User'
  }))),
  
  processUserInfoResponse: jest.fn().mockResolvedValue({
    sub: 'mock-user-id',
    email: '<EMAIL>',
    name: 'Mock User'
  }),
  
  generateRandomCodeVerifier: jest.fn().mockReturnValue('mock-code-verifier'),
  calculatePKCECodeChallenge: jest.fn().mockResolvedValue('mock-code-challenge'),
  generateRandomState: jest.fn().mockReturnValue('mock-state'),
  generateRandomNonce: jest.fn().mockReturnValue('mock-nonce'),
  
  validateAuthResponse: jest.fn().mockReturnValue(new URL('https://mock-callback.com')),
  
  isOAuth2Error: jest.fn().mockReturnValue(false),
  parseWwwAuthenticateChallenges: jest.fn().mockReturnValue([])
};
