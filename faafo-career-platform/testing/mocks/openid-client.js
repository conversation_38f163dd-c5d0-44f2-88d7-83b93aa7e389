// Mock for openid-client module
class MockClient {
  constructor() {
    this.authorizationUrl = jest.fn().mockReturnValue('https://mock-auth-url.com');
    this.callbackParams = jest.fn().mockReturnValue({});
    this.callback = jest.fn().mockResolvedValue({
      access_token: 'mock-access-token',
      id_token: 'mock-id-token'
    });
    this.userinfo = jest.fn().mockResolvedValue({
      sub: 'mock-user-id',
      email: '<EMAIL>',
      name: 'Mock User'
    });
  }
}

class MockIssuer {
  static async discover() {
    return new MockIssuer();
  }

  get Client() {
    return MockClient;
  }
}

module.exports = {
  Issuer: MockIssuer,
  
  generators: {
    codeVerifier: jest.fn().mockReturnValue('mock-code-verifier'),
    codeChallenge: jest.fn().mockReturnValue('mock-code-challenge'),
    state: jest.fn().mockReturnValue('mock-state'),
    nonce: jest.fn().mockReturnValue('mock-nonce')
  },
  
  errors: {
    OPError: class MockOPError extends Error {
      constructor(message) {
        super(message);
        this.name = 'OPError';
      }
    },
    RPError: class MockRPError extends Error {
      constructor(message) {
        super(message);
        this.name = 'RPError';
      }
    }
  }
};
