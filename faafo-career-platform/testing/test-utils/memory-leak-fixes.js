// Memory leak fixes for Jest tests

function setupJestMemoryFixes() {
  // Prevent memory leaks in tests
  afterEach(() => {
    // Clear all timers
    jest.clearAllTimers()
    
    // Clear all mocks
    jest.clearAllMocks()
  })

  // Global cleanup
  afterAll(() => {
    // Force garbage collection if available
    if (global.gc) {
      global.gc()
    }
  })
}

module.exports = {
  setupJestMemoryFixes
}
