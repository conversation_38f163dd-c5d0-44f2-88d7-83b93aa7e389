/**
 * Basic utility tests to verify test setup
 */

describe('Basic Test Setup', () => {
  it('should run basic tests', () => {
    expect(1 + 1).toBe(2)
  })

  it('should have access to environment variables', () => {
    expect(process.env.NODE_ENV).toBe('test')
  })

  it('should have Jest globals available', () => {
    expect(jest).toBeDefined()
    expect(describe).toBeDefined()
    expect(it).toBeDefined()
    expect(expect).toBeDefined()
  })

  it('should handle async operations', async () => {
    const promise = Promise.resolve('test')
    const result = await promise
    expect(result).toBe('test')
  })

  it('should handle timeouts properly', async () => {
    const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))
    
    const start = Date.now()
    await delay(100)
    const end = Date.now()
    
    expect(end - start).toBeGreaterThanOrEqual(90)
  }, 10000)
})

describe('TypeScript Support', () => {
  it('should support TypeScript types', () => {
    interface TestInterface {
      name: string
      value: number
    }

    const testObj: TestInterface = {
      name: 'test',
      value: 42
    }

    expect(testObj.name).toBe('test')
    expect(testObj.value).toBe(42)
  })

  it('should support modern JavaScript features', () => {
    const arr = [1, 2, 3, 4, 5]
    const doubled = arr.map(x => x * 2)
    const filtered = doubled.filter(x => x > 5)
    
    expect(filtered).toEqual([6, 8, 10])
  })
})
