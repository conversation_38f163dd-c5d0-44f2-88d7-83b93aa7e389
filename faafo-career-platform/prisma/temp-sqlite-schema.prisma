
generator client {
  provider = "prisma-client-js"
  output   = "./generated/sqlite-client"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model User {
  id    String @id @default(cuid())
  email String @unique
  name  String?
  // Add other fields as needed
}

model LearningResource {
  id          String @id @default(uuid())
  title       String
  description String
  url         String @unique
  type        String
  // Add other fields as needed
}
