import '@testing-library/jest-dom'

// Ensure React is available globally for JSX
import React from 'react'
global.React = React

// Mock React hooks that might cause issues
const mockUseMemo = jest.fn((fn, deps) => fn())
const mockUseCallback = jest.fn((fn, deps) => fn)
const mockUseEffect = jest.fn((fn, deps) => fn())
const mockUseState = jest.fn((initial) => [initial, jest.fn()]) as any

// Only mock if React hooks are null/undefined (shouldn't happen but safety check)
if (!React.useMemo) React.useMemo = mockUseMemo
if (!React.useCallback) React.useCallback = mockUseCallback
if (!React.useEffect) React.useEffect = mockUseEffect
if (!React.useState) React.useState = mockUseState

// Mock environment variables
if (!process.env['NODE_ENV']) {
  (process.env as any)['NODE_ENV'] = 'test'
}
if (!process.env['DATABASE_URL']) {
  (process.env as any)['DATABASE_URL'] = 'postgres://test:test@localhost:5432/test'
}
if (!process.env['NEXTAUTH_SECRET']) {
  (process.env as any)['NEXTAUTH_SECRET'] = 'test-secret'
}
if (!process.env['NEXTAUTH_URL']) {
  (process.env as any)['NEXTAUTH_URL'] = 'http://localhost:3000'
}

// Global test timeout
jest.setTimeout(30000)

// Suppress console warnings in tests
const originalWarn = console.warn
const originalError = console.error

beforeAll(() => {
  console.warn = (...args: any[]) => {
    if (
      typeof args[0] === 'string' &&
      (args[0].includes('Warning: ReactDOM.render is deprecated') ||
       args[0].includes('Warning: React.createFactory is deprecated'))
    ) {
      return
    }
    originalWarn.call(console, ...args)
  }

  console.error = (...args: any[]) => {
    if (
      typeof args[0] === 'string' &&
      (args[0].includes('Warning:') || 
       args[0].includes('Error: Not implemented'))
    ) {
      return
    }
    originalError.call(console, ...args)
  }
})

afterAll(() => {
  console.warn = originalWarn
  console.error = originalError
})
