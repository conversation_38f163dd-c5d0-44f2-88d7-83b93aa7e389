require('dotenv').config({ path: '.env.local' });
require('dotenv').config({ path: '.env' });

const { PrismaClient } = require('@prisma/client');

async function testCriticalOperations() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🧪 Testing critical database operations...');
    console.log('DATABASE_URL:', process.env.DATABASE_URL);
    
    // Test 1: Basic connection
    await prisma.$connect();
    console.log('✅ Test 1: Database connection successful!');
    
    // Test 2: Count operations on main tables
    const userCount = await prisma.user.count();
    console.log(`✅ Test 2: User count query successful! Found ${userCount} users.`);
    
    const profileCount = await prisma.profile.count();
    console.log(`✅ Test 2: Profile count query successful! Found ${profileCount} profiles.`);
    
    // Test 3: Test reading from assessment tables
    const assessmentCount = await prisma.assessment.count();
    console.log(`✅ Test 3: Assessment count query successful! Found ${assessmentCount} assessments.`);
    
    // Test 4: Test reading from career path tables
    const careerPathCount = await prisma.careerPath.count();
    console.log(`✅ Test 4: Career path count query successful! Found ${careerPathCount} career paths.`);
    
    // Test 5: Test complex query with joins
    const usersWithProfiles = await prisma.user.findMany({
      include: {
        profile: true
      },
      take: 5
    });
    console.log(`✅ Test 5: Complex join query successful! Found ${usersWithProfiles.length} users with profiles.`);
    
    // Test 6: Test reading from learning resources
    const resourceCount = await prisma.learningResource.count();
    console.log(`✅ Test 6: Learning resource count query successful! Found ${resourceCount} resources.`);
    
    // Test 7: Test forum functionality
    const forumPostCount = await prisma.forumPost.count();
    console.log(`✅ Test 7: Forum post count query successful! Found ${forumPostCount} posts.`);
    
    // Test 8: Test bookmark functionality
    const forumBookmarkCount = await prisma.forumBookmark.count();
    console.log(`✅ Test 8a: Forum bookmark count query successful! Found ${forumBookmarkCount} forum bookmarks.`);

    const careerBookmarkCount = await prisma.careerPathBookmark.count();
    console.log(`✅ Test 8b: Career path bookmark count query successful! Found ${careerBookmarkCount} career bookmarks.`);
    
    console.log('🎉 All critical database operations passed!');
    
  } catch (error) {
    console.error('❌ Critical database operation failed:');
    console.error('Error:', error.message);
    console.error('Code:', error.code);
    if (error.meta) {
      console.error('Meta:', error.meta);
    }
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

testCriticalOperations();
