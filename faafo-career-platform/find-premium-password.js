const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const prisma = new PrismaClient();

async function findPassword() {
  try {
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      select: { password: true, failedLoginAttempts: true }
    });
    
    if (!user) {
      console.log('❌ <EMAIL> not found');
      return;
    }
    
    console.log('🔍 Testing common <NAME_EMAIL>:');
    const passwords = [
      'premium', 'Premium123!', 'testpassword', 'password123', 
      'password', 'test123', 'Premium123', 'premium123',
      'TestPassword123!', 'PremiumUser123!', 'Premium@123',
      'premium@test', 'Premium@Test', 'PremiumTest123!'
    ];
    
    for (const pwd of passwords) {
      const isValid = await bcrypt.compare(pwd, user.password);
      if (isValid) {
        console.log(`✅ FOUND: ${pwd}`);
        break;
      } else {
        console.log(`❌ ${pwd}`);
      }
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

findPassword();
