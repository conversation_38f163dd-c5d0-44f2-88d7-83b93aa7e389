import React, { useState, memo, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { getDemandColor, getSeverityColor, getSeverityBgColor } from '@/lib/design-system-colors';
import { SkillGapLearningResource, Priority } from '@/types/learning-resource';
import { UnifiedTimeCalculator, TimeCalculationParams } from '@/lib/unified-time-calculator';
import {
  TrendingUp,
  TrendingDown,
  Target,
  Clock,
  BookOpen,
  CheckCircle,
  AlertTriangle,
  Star,
  Users,
  Lightbulb,
  Calendar,
  DollarSign
} from 'lucide-react';

interface SkillGap {
  skillId: string;
  skillName: string;
  currentLevel: number;
  targetLevel: number;
  gapSeverity: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';
  priority: number;
  estimatedLearningTime: number;
  marketDemand: 'VERY_HIGH' | 'HIGH' | 'MODERATE' | 'LOW' | 'VERY_LOW';
  salaryImpact: number;
}

interface LearningMilestone {
  month: number;
  skills: string[];
  estimatedHours: number;
  learningPaths: string[];
  objectives?: string[];
  assessmentCriteria?: string[];
}

// Use the unified LearningResource interface
type LearningResource = SkillGapLearningResource;

interface LearningPlan {
  totalEstimatedHours: number;
  milestones: LearningMilestone[];
  recommendedResources: LearningResource[];
}

interface CareerReadiness {
  currentScore: number;
  targetScore: number;
  improvementPotential: number;
  timeToTarget: number;
  confidenceLevel?: number;
  marketCompetitiveness?: number;
}

interface MarketInsights {
  industryTrends: Array<{
    skill: string;
    trend: 'DECLINING' | 'STABLE' | 'GROWING' | 'RAPIDLY_GROWING' | 'EMERGING';
    demandLevel: 'VERY_HIGH' | 'HIGH' | 'MODERATE' | 'LOW' | 'VERY_LOW';
    futureOutlook?: string;
  }>;
  salaryProjections: {
    currentEstimate: number;
    targetEstimate: number;
    improvementPotential: number;
    timeToRealization?: number;
  };
  competitiveAdvantage?: Array<{
    skill: string;
    advantage: string;
    rarity: 'COMMON' | 'UNCOMMON' | 'RARE' | 'VERY_RARE';
  }>;
}

interface SkillGapAnalysisProps {
  analysisId: string;
  skillGaps: SkillGap[];
  learningPlan: LearningPlan;
  careerReadiness: CareerReadiness;
  marketInsights?: MarketInsights;
  generatedAt: string;
  cached?: boolean;
  edgeCaseHandlerData?: {
    sanitizedInput?: any;
    isNewUser?: boolean;
    onboardingRecommendations?: string[];
    retryCount?: number;
    fallbackDataUsed?: boolean;
    suggestedAlternatives?: string[];
  };
}

function SkillGapAnalysisComponent({
  analysisId,
  skillGaps,
  learningPlan,
  careerReadiness,
  marketInsights,
  generatedAt,
  cached = false,
  edgeCaseHandlerData
}: SkillGapAnalysisProps) {
  const [selectedTab, setSelectedTab] = useState('overview');
  const [errorState, setErrorState] = useState<{
    hasError: boolean;
    errorMessage: string;
    errorDetails?: string;
    recoverable: boolean;
  }>({
    hasError: false,
    errorMessage: '',
    errorDetails: '',
    recoverable: true
  });

  // Progress tracking state for milestone completion
  const [milestoneProgress, setMilestoneProgress] = useState<Record<string, {
    completed: boolean;
    completedDate?: string;
    progress: number; // 0-100
    skillsCompleted: string[];
  }>>({});

  // Enhanced error handling system with user-friendly messages and graceful degradation
  const errorHandler = {
    // Handle errors with user-friendly messages
    handleError: (error: any, context: string, recoverable: boolean = true) => {
      const errorMessage = errorHandler.getErrorMessage(error, context);
      const errorDetails = error?.message || error?.toString() || 'Unknown error';

      console.error(`[SkillGapAnalysis] ${context}:`, error);

      setErrorState({
        hasError: true,
        errorMessage,
        errorDetails,
        recoverable
      });

      // Auto-recover for recoverable errors after 5 seconds
      if (recoverable) {
        setTimeout(() => {
          setErrorState(prev => ({ ...prev, hasError: false }));
        }, 5000);
      }
    },

    // Generate user-friendly error messages
    getErrorMessage: (error: any, context: string): string => {
      const errorMessages: Record<string, string> = {
        'data-loading': 'We\'re having trouble loading your career data. Using sample data to continue.',
        'skill-processing': 'Some skill data couldn\'t be processed. We\'ve filled in reasonable defaults.',
        'time-calculation': 'Learning time calculations are temporarily unavailable. Showing estimated values.',
        'market-insights': 'Market data is currently unavailable. Showing general industry trends.',
        'learning-plan': 'Your learning plan couldn\'t be generated. We\'ve created a sample plan to get you started.',
        'progress-tracking': 'Progress tracking is temporarily unavailable. Your progress will be saved when the system recovers.',
        'date-formatting': 'Date formatting encountered an issue. Using current date as fallback.'
      };

      return errorMessages[context] || 'We encountered a temporary issue. The system has automatically recovered.';
    },

    // Clear error state
    clearError: () => {
      setErrorState({
        hasError: false,
        errorMessage: '',
        errorDetails: '',
        recoverable: true
      });
    }
  };

  // Comprehensive data validation pipeline - prevents NaN/null values from reaching UI
  const dataValidationPipeline = {
    // Sanitize numeric values with fallbacks
    sanitizeNumber: (value: any, fallback: number = 0, min: number = 0, max: number = Infinity): number => {
      if (typeof value === 'number' && !isNaN(value) && isFinite(value)) {
        return Math.max(min, Math.min(max, value));
      }
      if (typeof value === 'string') {
        const parsed = parseFloat(value);
        if (!isNaN(parsed) && isFinite(parsed)) {
          return Math.max(min, Math.min(max, parsed));
        }
      }
      return fallback;
    },

    // Sanitize string values with fallbacks
    sanitizeString: (value: any, fallback: string = 'N/A'): string => {
      if (typeof value === 'string' && value.trim() !== '') {
        return value.trim();
      }
      if (value !== null && value !== undefined && typeof value.toString === 'function') {
        const stringValue = value.toString().trim();
        return stringValue !== '' ? stringValue : fallback;
      }
      return fallback;
    },

    // Sanitize arrays with fallbacks
    sanitizeArray: (value: any, fallback: any[] = []): any[] => {
      if (Array.isArray(value)) {
        return value.filter(item => item !== null && item !== undefined);
      }
      return fallback;
    },

    // Validate and sanitize skill gap object
    sanitizeSkillGap: (gap: any): SkillGap | null => {
      if (!gap || typeof gap !== 'object') return null;

      try {
        const skillName = dataValidationPipeline.sanitizeString(gap.skillName || gap.skill, 'Unknown Skill');
        const currentLevel = dataValidationPipeline.sanitizeNumber(gap.currentLevel, 1, 1, 10);
        const targetLevel = dataValidationPipeline.sanitizeNumber(gap.targetLevel, 5, 1, 10);
        const gapSize = Math.max(1, targetLevel - currentLevel);

        // Use unified time calculator for consistent time estimates
        const timeEstimate = UnifiedTimeCalculator.calculateLearningTime({
          gapSize,
          skillName,
          difficulty: 5 // Default difficulty
        });

        return {
          skillId: dataValidationPipeline.sanitizeString(gap.skillId, `skill-${Date.now()}`),
          skillName,
          currentLevel,
          targetLevel,
          gapSeverity: ['CRITICAL', 'HIGH', 'MEDIUM', 'LOW'].includes(gap.gapSeverity)
            ? gap.gapSeverity
            : 'MEDIUM',
          priority: dataValidationPipeline.sanitizeNumber(gap.priority, 50, 0, 100),
          estimatedLearningTime: timeEstimate.hours, // Use unified calculation
          marketDemand: ['VERY_HIGH', 'HIGH', 'MODERATE', 'LOW', 'VERY_LOW'].includes(gap.marketDemand)
            ? gap.marketDemand
            : 'MODERATE',
          salaryImpact: dataValidationPipeline.sanitizeNumber(gap.salaryImpact, 10, 0, 100)
        };
      } catch (error) {
        errorHandler.handleError(error, 'skill-processing', true);
        return null;
      }
    }
  };

  // Enhanced career readiness validation with meaningful defaults
  const validateCareerReadiness = (readiness: any): boolean => {
    return readiness &&
           typeof readiness === 'object' &&
           typeof readiness.currentScore === 'number' &&
           typeof readiness.targetScore === 'number' &&
           readiness.targetScore > 0; // Must have meaningful target
  };

  const hasValidCareerReadiness = validateCareerReadiness(careerReadiness);

  // Enhanced career readiness with data validation pipeline
  const safeCareerReadiness = hasValidCareerReadiness
    ? {
        currentScore: dataValidationPipeline.sanitizeNumber(careerReadiness.currentScore, 25, 0, 100),
        targetScore: dataValidationPipeline.sanitizeNumber(careerReadiness.targetScore, 85, 0, 100),
        timeToTarget: dataValidationPipeline.sanitizeNumber(careerReadiness.timeToTarget, 8, 1, 24),
        confidenceLevel: dataValidationPipeline.sanitizeNumber(careerReadiness.confidenceLevel, 65, 0, 100)
      }
    : {
        currentScore: 25, // More realistic starting point
        targetScore: 85,  // Achievable target
        improvementPotential: 60,
        timeToTarget: 8,  // More realistic timeframe
        confidenceLevel: 65
      };

  // Calculate unified total learning time from skill gaps
  const unifiedTotalLearningTime = useMemo(() => {
    if (!skillGaps || skillGaps.length === 0) return { hours: 0, formattedString: '0h' };

    return UnifiedTimeCalculator.calculateTotalLearningPlanTime(
      skillGaps.map(gap => ({
        gapSize: gap.targetLevel - gap.currentLevel,
        estimatedLearningTime: gap.estimatedLearningTime,
        skillName: gap.skillName
      }))
    );
  }, [skillGaps]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-6 w-6 text-blue-600" />
                Skill Gap Analysis
              </CardTitle>
              <CardDescription>
                Generated on {new Date(generatedAt || Date.now()).toLocaleDateString()}
                <div className="flex gap-2 mt-1">
                  {cached && <Badge variant="secondary">Cached</Badge>}
                  {edgeCaseHandlerData?.fallbackDataUsed && (
                    <Badge variant="outline" className="text-orange-600 border-orange-600">
                      Fallback Data
                    </Badge>
                  )}
                </div>
              </CardDescription>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Error Display with User-Friendly Messages */}
      {errorState.hasError && (
        <Card className="border-orange-200 bg-orange-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-orange-700">
              <AlertTriangle className="h-5 w-5" />
              System Notice
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <p className="text-orange-800">{errorState.errorMessage}</p>
              {errorState.recoverable && (
                <div className="flex items-center gap-2 text-sm text-orange-600">
                  <Clock className="h-4 w-4" />
                  This notice will automatically disappear in a few seconds.
                </div>
              )}
              {!errorState.recoverable && (
                <button
                  onClick={errorHandler.clearError}
                  className="px-3 py-1 text-sm bg-orange-100 text-orange-700 rounded hover:bg-orange-200 transition-colors"
                >
                  Dismiss
                </button>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="gaps">Skill Gaps</TabsTrigger>
          <TabsTrigger value="plan">Learning Plan</TabsTrigger>
          <TabsTrigger value="market">Market Insights</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-2xl font-bold">{safeCareerReadiness.currentScore}%</p>
                    <p className="text-sm text-gray-500">Current Readiness</p>
                  </div>
                  <Target className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-2xl font-bold">{skillGaps?.length || 0}</p>
                    <p className="text-sm text-gray-500">Skill Gaps</p>
                  </div>
                  <BookOpen className="h-8 w-8 text-orange-600" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-2xl font-bold">{safeCareerReadiness.timeToTarget}mo</p>
                    <p className="text-sm text-gray-500">Time to Target</p>
                  </div>
                  <Clock className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Skill Gaps Tab */}
        <TabsContent value="gaps" className="space-y-4">
          {skillGaps?.map((gap, index) => (
            <Card
              key={gap.skillId || `gap-${index}`}
              className="border-l-4"
              style={{ borderLeftColor: getSeverityBgColor(gap.gapSeverity) }}
            >
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-lg">{gap.skillName}</CardTitle>
                    <div className="flex items-center gap-2 mt-2">
                      <Badge variant={gap.gapSeverity === 'CRITICAL' ? 'destructive' : 'secondary'}>
                        {gap.gapSeverity}
                      </Badge>
                      <Badge variant="outline">Priority: {gap.priority}</Badge>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Current Level: {gap.currentLevel}/10</span>
                      <span>Target Level: {gap.targetLevel}/10</span>
                    </div>
                    <Progress value={(gap.currentLevel / gap.targetLevel) * 100} className="h-2" />
                  </div>
                  <div className="text-sm text-gray-600">
                    Estimated learning time: {gap.estimatedLearningTime} hours
                  </div>
                </div>
              </CardContent>
            </Card>
          )) || (
            <Card>
              <CardContent className="pt-6">
                <p className="text-center text-gray-500">No skill gaps identified</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Learning Plan Tab */}
        <TabsContent value="plan" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Learning Plan Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-blue-600">
                    {unifiedTotalLearningTime.formattedString}
                  </div>
                  <div className="text-sm text-gray-500">Total Learning Time (Unified)</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">
                    {learningPlan?.milestones?.length || 0}
                  </div>
                  <div className="text-sm text-gray-500">Milestones</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Market Insights Tab */}
        <TabsContent value="market" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Market Insights</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold mb-2">Salary Projections</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-500">Current Estimate</p>
                      <p className="text-lg font-bold">
                        ${marketInsights?.salaryProjections?.currentEstimate?.toLocaleString() || '75,000'}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Target Estimate</p>
                      <p className="text-lg font-bold">
                        ${marketInsights?.salaryProjections?.targetEstimate?.toLocaleString() || '95,000'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

const SkillGapAnalysis = memo(SkillGapAnalysisComponent);

export default SkillGapAnalysis;
