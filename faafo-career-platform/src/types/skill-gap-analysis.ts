/**
 * Skill Gap Analysis Types
 * 
 * Comprehensive TypeScript types for skill gap analysis, career readiness,
 * market insights, and learning plan generation in the FAAFO Career Platform.
 */

import { SkillGapLearningResource, Priority } from './learning-resource';
import { LearningMilestone, ProgressTrackingState } from './progress-tracking';

// Core skill gap types
export interface SkillGap {
  skillId: string;
  skillName: string;
  currentLevel: number; // 1-10
  targetLevel: number; // 1-10
  gapSeverity: GapSeverity;
  priority: number; // 0-100
  estimatedLearningTime: number; // hours
  marketDemand: MarketDemand;
  salaryImpact: number; // percentage
}

export type GapSeverity = 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';

export type MarketDemand = 'VERY_HIGH' | 'HIGH' | 'MODERATE' | 'LOW' | 'VERY_LOW';

// Learning plan types
export interface LearningPlan {
  totalEstimatedHours: number;
  milestones: LearningMilestone[];
  recommendedResources: SkillGapLearningResource[];
}

export interface LearningPlanValidation {
  isValid: boolean;
  hasValidMilestones: boolean;
  hasValidResources: boolean;
  timeConsistency: TimeConsistencyCheck;
  errors: string[];
  warnings: string[];
}

export interface TimeConsistencyCheck {
  isConsistent: boolean;
  difference: number; // hours
  milestoneTotal?: number;
  skillGapTotal?: number;
}

// Career readiness types
export interface CareerReadiness {
  currentScore: number; // 0-100
  targetScore: number; // 0-100
  improvementPotential: number; // percentage
  timeToTarget: number; // months
  confidenceLevel?: number; // 0-100
  marketCompetitiveness?: number; // 0-100
}

export interface CareerReadinessValidation {
  isValid: boolean;
  hasValidScores: boolean;
  hasRealisticTargets: boolean;
  errors: string[];
  suggestions: string[];
}

// Market insights types
export interface MarketInsights {
  industryTrends: IndustryTrend[];
  salaryProjections: SalaryProjections;
  competitiveAdvantage?: CompetitiveAdvantage[];
  competitiveAnalysis?: CompetitiveAnalysis;
}

export interface IndustryTrend {
  skill: string;
  trend: TrendDirection;
  demandLevel: MarketDemand;
  futureOutlook?: string;
  growthRate?: number; // percentage
  timeframe?: string;
}

export type TrendDirection = 'DECLINING' | 'STABLE' | 'GROWING' | 'RAPIDLY_GROWING' | 'EMERGING';

export interface SalaryProjections {
  currentEstimate: number;
  targetEstimate: number;
  improvementPotential: number; // percentage
  timeToRealization?: number; // months
  confidenceLevel?: number; // 0-100
}

export interface CompetitiveAdvantage {
  skill: string;
  advantage: string;
  rarity: SkillRarity;
  marketValue: number; // 1-10
  differentiationPotential: number; // percentage
}

export type SkillRarity = 'COMMON' | 'UNCOMMON' | 'RARE' | 'VERY_RARE';

export interface CompetitiveAnalysis {
  marketPosition: MarketPosition;
  competitorComparison: CompetitorComparison;
  differentiationOpportunities: string[];
  strengthAreas: string[];
  improvementAreas: string[];
}

export type MarketPosition = 'ENTRY_LEVEL' | 'DEVELOPING' | 'STRONG' | 'EXPERT' | 'THOUGHT_LEADER';

export type CompetitorComparison = 'BELOW_AVERAGE' | 'AVERAGE' | 'ABOVE_AVERAGE' | 'TOP_TIER';

// Analysis request and response types
export interface SkillGapAnalysisRequest {
  userId: string;
  currentSkills: UserSkill[];
  targetCareerPath: CareerPathTarget;
  preferences?: AnalysisPreferences;
  includeMarketData?: boolean;
  includePersonalizedPaths?: boolean;
}

export interface UserSkill {
  skillName: string;
  selfRating: number; // 1-10
  confidenceLevel: number; // 1-10
  yearsExperience?: number;
  lastUsed?: Date;
  certifications?: string[];
}

export interface CareerPathTarget {
  careerPathName: string;
  targetLevel: ExperienceLevel;
  timeframe?: number; // months
  location?: string;
  salaryExpectation?: number;
}

export type ExperienceLevel = 'ENTRY' | 'JUNIOR' | 'MID' | 'SENIOR' | 'LEAD' | 'EXECUTIVE';

export interface AnalysisPreferences {
  learningStyle?: LearningStyle;
  timeCommitment?: TimeCommitment;
  budgetRange?: BudgetRange;
  priorityFocus?: PriorityFocus;
  includeRemoteOptions?: boolean;
  includeCertifications?: boolean;
}

export type LearningStyle = 'VISUAL' | 'AUDITORY' | 'KINESTHETIC' | 'READING' | 'MIXED';

export type TimeCommitment = 'MINIMAL' | 'MODERATE' | 'INTENSIVE' | 'FULL_TIME';

export type BudgetRange = 'FREE' | 'LOW' | 'MODERATE' | 'HIGH' | 'UNLIMITED';

export type PriorityFocus = 'SPEED' | 'DEPTH' | 'BREADTH' | 'CERTIFICATION' | 'PRACTICAL';

// Analysis result types
export interface SkillGapAnalysisResult {
  analysisId: string;
  userId: string;
  skillGaps: SkillGap[];
  learningPlan: LearningPlan;
  careerReadiness: CareerReadiness;
  marketInsights?: MarketInsights;
  generatedAt: string;
  cached?: boolean;
  metadata?: AnalysisMetadata;
}

export interface AnalysisMetadata {
  version: string;
  model: string;
  processingTime: number; // milliseconds
  dataQuality: DataQuality;
  edgeCaseHandlerData?: EdgeCaseHandlerData;
  fallbackDataUsed?: boolean;
  cacheHit?: boolean;
}

export interface DataQuality {
  skillDataCompleteness: number; // 0-100
  marketDataFreshness: number; // 0-100
  analysisConfidence: number; // 0-100
  recommendationReliability: number; // 0-100
}

export interface EdgeCaseHandlerData {
  sanitizedInput?: any;
  isNewUser?: boolean;
  onboardingRecommendations?: string[];
  retryCount?: number;
  fallbackDataUsed?: boolean;
  suggestedAlternatives?: string[];
}

// Component props types
export interface SkillGapAnalysisProps {
  analysisId: string;
  skillGaps: SkillGap[];
  learningPlan: LearningPlan;
  careerReadiness: CareerReadiness;
  marketInsights?: MarketInsights;
  generatedAt: string;
  cached?: boolean;
  edgeCaseHandlerData?: EdgeCaseHandlerData;
  onProgressUpdate?: (progress: ProgressTrackingState) => void;
  onRetry?: () => void;
  onExport?: (format: ExportFormat) => void;
}

export type ExportFormat = 'PDF' | 'JSON' | 'CSV' | 'EXCEL';

// Validation and utility types
export interface SkillGapValidation {
  isValid: boolean;
  hasValidSkills: boolean;
  hasRealisticLevels: boolean;
  hasValidPriorities: boolean;
  errors: string[];
  warnings: string[];
}

export interface AnalysisQualityMetrics {
  dataCompleteness: number; // 0-100
  analysisDepth: number; // 0-100
  recommendationRelevance: number; // 0-100
  marketDataAccuracy: number; // 0-100
  overallQuality: number; // 0-100
}

// Type guards for runtime validation
export function isSkillGap(obj: any): obj is SkillGap {
  return obj &&
         typeof obj.skillId === 'string' &&
         typeof obj.skillName === 'string' &&
         typeof obj.currentLevel === 'number' &&
         typeof obj.targetLevel === 'number' &&
         typeof obj.gapSeverity === 'string' &&
         typeof obj.priority === 'number' &&
         typeof obj.estimatedLearningTime === 'number' &&
         obj.currentLevel >= 1 && obj.currentLevel <= 10 &&
         obj.targetLevel >= 1 && obj.targetLevel <= 10 &&
         obj.priority >= 0 && obj.priority <= 100;
}

export function isLearningPlan(obj: any): obj is LearningPlan {
  return obj &&
         typeof obj.totalEstimatedHours === 'number' &&
         Array.isArray(obj.milestones) &&
         Array.isArray(obj.recommendedResources) &&
         obj.totalEstimatedHours > 0;
}

export function isCareerReadiness(obj: any): obj is CareerReadiness {
  return obj &&
         typeof obj.currentScore === 'number' &&
         typeof obj.targetScore === 'number' &&
         typeof obj.improvementPotential === 'number' &&
         typeof obj.timeToTarget === 'number' &&
         obj.currentScore >= 0 && obj.currentScore <= 100 &&
         obj.targetScore >= 0 && obj.targetScore <= 100 &&
         obj.timeToTarget > 0;
}

export function isMarketInsights(obj: any): obj is MarketInsights {
  return obj &&
         Array.isArray(obj.industryTrends) &&
         obj.salaryProjections &&
         typeof obj.salaryProjections.currentEstimate === 'number' &&
         typeof obj.salaryProjections.targetEstimate === 'number';
}

// Utility functions
export function calculateGapSize(currentLevel: number, targetLevel: number): number {
  return Math.max(0, targetLevel - currentLevel);
}

export function calculatePriorityScore(
  gapSize: number,
  severity: GapSeverity,
  marketDemand: MarketDemand,
  salaryImpact: number
): number {
  const severityWeight = { CRITICAL: 40, HIGH: 30, MEDIUM: 20, LOW: 10 };
  const demandWeight = { VERY_HIGH: 30, HIGH: 25, MODERATE: 15, LOW: 10, VERY_LOW: 5 };
  
  const gapWeight = Math.min(gapSize * 5, 20); // Max 20 points for gap size
  const severityPoints = severityWeight[severity] || 0;
  const demandPoints = demandWeight[marketDemand] || 0;
  const salaryPoints = Math.min(salaryImpact, 10); // Max 10 points for salary impact
  
  return Math.min(100, gapWeight + severityPoints + demandPoints + salaryPoints);
}

/**
 * DEPRECATED: Use UnifiedTimeCalculator.calculateLearningTime() instead
 * This function is kept for backward compatibility but redirects to the unified calculator
 * to fix contradictory time metrics (L001)
 */
export function estimateLearningTime(
  gapSize: number,
  difficulty: number,
  learningStyle?: LearningStyle,
  timeCommitment?: TimeCommitment
): number {
  // Use require to avoid circular dependencies (works in Node.js environment)
  let UnifiedTimeCalculator: any;
  try {
    UnifiedTimeCalculator = require('@/lib/unified-time-calculator').UnifiedTimeCalculator;
  } catch (error) {
    // Fallback if import fails
    console.warn('Could not import UnifiedTimeCalculator, using fallback calculation');
  }

  try {
    if (UnifiedTimeCalculator) {
      const timeEstimate = UnifiedTimeCalculator.calculateLearningTime({
        gapSize,
        difficulty,
        learningStyle,
        timeCommitment
      });

      return timeEstimate.hours;
    }
  } catch (error) {
    console.error('Error in deprecated estimateLearningTime, using fallback:', error);
  }

  // Fallback calculation if unified calculator is not available or fails
  const baseHours = gapSize * 12; // Updated to match unified calculator base
  const difficultyMultiplier = 1 + (difficulty - 5) * 0.1;

  let styleMultiplier = 1;
  if (learningStyle === 'KINESTHETIC') styleMultiplier = 1.2;
  if (learningStyle === 'VISUAL') styleMultiplier = 0.9;

  let commitmentMultiplier = 1;
  if (timeCommitment === 'INTENSIVE') commitmentMultiplier = 0.8;
  if (timeCommitment === 'MINIMAL') commitmentMultiplier = 1.3;

  return Math.round(baseHours * difficultyMultiplier * styleMultiplier * commitmentMultiplier);
}

// Default values and constants
export const DEFAULT_SKILL_GAP: Partial<SkillGap> = {
  currentLevel: 1,
  targetLevel: 5,
  gapSeverity: 'MEDIUM',
  priority: 50,
  estimatedLearningTime: 40,
  marketDemand: 'MODERATE',
  salaryImpact: 10
};

export const DEFAULT_CAREER_READINESS: CareerReadiness = {
  currentScore: 25,
  targetScore: 85,
  improvementPotential: 60,
  timeToTarget: 8,
  confidenceLevel: 65,
  marketCompetitiveness: 50
};

export const ANALYSIS_CONSTANTS = {
  MIN_SKILL_LEVEL: 1,
  MAX_SKILL_LEVEL: 10,
  MIN_PRIORITY: 0,
  MAX_PRIORITY: 100,
  MIN_CAREER_SCORE: 0,
  MAX_CAREER_SCORE: 100,
  DEFAULT_ANALYSIS_TIMEOUT: 30000, // 30 seconds
  MAX_SKILL_GAPS: 20,
  MIN_LEARNING_HOURS: 10,
  MAX_LEARNING_HOURS: 1000
} as const;
