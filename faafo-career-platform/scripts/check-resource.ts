import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkResource() {
  console.log('🔍 Checking if resource with ID "1" exists...');

  try {
    // Check if resource exists
    const resource = await prisma.learningResource.findUnique({
      where: { id: '1' }
    });

    if (resource) {
      console.log('✅ Resource found:', resource.title);
      console.log('📝 Resource details:', {
        id: resource.id,
        title: resource.title,
        type: resource.type,
        category: resource.category,
        isActive: resource.isActive
      });
    } else {
      console.log('❌ Resource with ID "1" not found');
      
      // Create the resource
      console.log('🌱 Creating resource with ID "1"...');
      const newResource = await prisma.learningResource.create({
        data: {
          id: '1',
          title: 'Overcoming Six Fears of Midlife Career Change',
          description: 'A comprehensive guide to understanding and conquering the fears that hold you back from making a career transition.',
          url: 'https://www.linkedin.com/pulse/overcoming-six-fears-midlife-career-change-guide-joanne-savoie-malone-xwpme',
          type: 'ARTICLE',
          category: 'ENTREPRENEURSHIP',
          skillLevel: 'BEGINNER',
          author: '<PERSON> Savoie-<PERSON>',
          duration: '8 min read',
          cost: 'FREE',
          format: 'SELF_PACED',
          isActive: true
        }
      });
      
      console.log('✅ Resource created:', newResource.title);
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the check
checkResource().catch(console.error);
