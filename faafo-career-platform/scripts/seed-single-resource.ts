import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function seedSingleResource() {
  console.log('🌱 Seeding single resource for testing...');

  try {
    // Create the specific resource that's being tested (ID: "1")
    const resource = {
      id: '1',
      title: 'Overcoming Six Fears of Midlife Career Change',
      description: 'A comprehensive guide to understanding and conquering the fears that hold you back from making a career transition.',
      url: 'https://www.linkedin.com/pulse/overcoming-six-fears-midlife-career-change-guide-joanne-savoie-malone-xwpme',
      type: 'ARTICLE',
      category: 'OVERCOMING_FEAR_ANXIETY',
      skillLevel: 'BEGINNER',
      author: '<PERSON>',
      duration: '8 min read',
      cost: 'FREE',
      format: 'SELF_PACED',
      isActive: true
    };

    // Check if resource already exists
    const existing = await prisma.learningResource.findUnique({
      where: { id: resource.id }
    });

    if (existing) {
      console.log(`⏭️  Resource already exists: ${resource.title}`);
      return;
    }

    // Create the resource
    const created = await prisma.learningResource.create({
      data: resource as any
    });

    console.log(`✅ Created resource: ${created.title}`);
    console.log(`📝 Resource ID: ${created.id}`);
    
  } catch (error) {
    console.error('❌ Error creating resource:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding
seedSingleResource().catch(console.error);
