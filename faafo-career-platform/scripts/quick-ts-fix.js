#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Quick TypeScript Fix Script');

// Common fixes to apply
const fixes = [
  // Remove SQLite incompatible mode options
  {
    pattern: /,\s*mode:\s*['"]insensitive['"]/g,
    replacement: '',
    description: 'Remove SQLite incompatible mode options'
  },
  
  // Fix environment variable access
  {
    pattern: /process\.env\.([A-Z_]+)/g,
    replacement: "process.env['$1']",
    description: 'Fix environment variable access'
  },
  
  // Add type suppressions for complex objects
  {
    pattern: /(\w+):\s*\{([^}]+)\}\s*as\s*any/g,
    replacement: '$1: $2 as any',
    description: 'Simplify type assertions'
  }
];

// Get all TypeScript files
function getAllTsFiles(dir) {
  const files = [];
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
      files.push(...getAllTsFiles(fullPath));
    } else if (item.endsWith('.ts') || item.endsWith('.tsx')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

// Apply fixes to a file
function applyFixes(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let changed = false;
    
    for (const fix of fixes) {
      const originalContent = content;
      content = content.replace(fix.pattern, fix.replacement);
      if (content !== originalContent) {
        changed = true;
        console.log(`  ✅ Applied: ${fix.description}`);
      }
    }
    
    if (changed) {
      fs.writeFileSync(filePath, content);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`  ❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main execution
function main() {
  const srcDir = path.join(process.cwd(), 'src');
  const files = getAllTsFiles(srcDir);
  
  console.log(`📁 Found ${files.length} TypeScript files`);
  
  let fixedFiles = 0;
  
  for (const file of files) {
    const relativePath = path.relative(process.cwd(), file);
    console.log(`🔧 Processing: ${relativePath}`);
    
    if (applyFixes(file)) {
      fixedFiles++;
    }
  }
  
  console.log(`\n🎉 Quick fixes complete!`);
  console.log(`📊 Fixed ${fixedFiles} files`);
  
  // Test build
  console.log('\n🔍 Testing build...');
  try {
    execSync('npm run build', { stdio: 'pipe' });
    console.log('✅ Build successful!');
  } catch (error) {
    console.log('⚠️  Build still has issues, but should be much better');
    console.log('Run npm run build to see remaining errors');
  }
}

if (require.main === module) {
  main();
}

module.exports = { applyFixes, getAllTsFiles };
