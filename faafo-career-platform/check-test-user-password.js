const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const prisma = new PrismaClient();

async function checkPassword() {
  try {
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      select: {
        id: true,
        email: true,
        password: true,
        emailVerified: true,
        failedLoginAttempts: true,
        lockedUntil: true
      }
    });
    
    if (!user) {
      console.log('❌ User not found');
      return;
    }
    
    console.log('👤 User Details:');
    console.log('Email:', user.email);
    console.log('Email Verified:', user.emailVerified ? '✅' : '❌');
    console.log('Failed Login Attempts:', user.failedLoginAttempts);
    console.log('Locked Until:', user.lockedUntil || 'Not locked');
    console.log('Password Hash:', user.password.substring(0, 20) + '...');
    
    // Test the password
    const testPasswords = ['TestPassword123!', 'testpassword', 'password123', 'password'];
    
    console.log('\n🔍 Testing passwords:');
    for (const pwd of testPasswords) {
      const isValid = await bcrypt.compare(pwd, user.password);
      console.log(`   ${pwd}: ${isValid ? '✅ MATCH' : '❌ No match'}`);
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkPassword();
